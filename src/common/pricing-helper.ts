import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { YEARLY_DISCOUNT_RATE } from '@constants/pricing.constants';
import moment from 'moment';

/**
 * Interface for subscription price calculation result
 */
export interface SubscriptionPriceResult {
  // First period (prorated or initial month)
  firstPeriod: {
    startDate: Date;
    endDate: Date;
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
    isProrated: boolean;
    label: string;
    formattedStartDate: string;
    formattedEndDate: string;
  };
  // Full month period
  fullMonthPeriod: {
    startDate: Date;
    endDate: Date;
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
    formattedStartDate: string;
    formattedEndDate: string;
  };
  // Combined amounts
  combined: {
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
  };
  // Subscription details
  subscriptionEndDate: Date;
  formattedSubscriptionEndDate: string;
  // Billing scenario details
  isFirstDayOfMonth: boolean;
  isAfterMidMonth: boolean;
  dayOfMonth: number;
}

/**
 * Calculate a single period price (prorated or full)
 * @param monthlyTotal The total monthly cost for all members
 * @param isProrated Whether to calculate prorated amount
 * @param isYearly Whether the billing is yearly
 * @param startDate The start date of the period
 * @param endDate The end date of the period
 * @returns The calculated price in cents
 */
const calculatePeriodPrice = (
  monthlyTotal: number,
  isProrated: boolean,
  isYearly: boolean,
  startDate: moment.Moment,
  endDate: moment.Moment
): { baseAmountInCents: number; sstAmountInCents: number; totalAmountInCents: number } => {
  let total = monthlyTotal;

  if (isProrated) {
    // Calculate days according to billing flow documentation
    const firstDayOfMonth = moment(startDate).date(1);
    const daysTotal = endDate.diff(firstDayOfMonth, 'days') + 1;
    const daysRemaining = endDate.diff(startDate, 'days');

    // Calculate prorated amount
    total = (monthlyTotal / daysTotal) * daysRemaining;
    total = Math.round(total * 100) / 100;
  } else if (isYearly) {
    // If yearly, multiply by 12 (full amount)
    total = Math.round(monthlyTotal * 12);
  }

  // Convert base amount to cents
  const baseAmountInCents = Math.round(total * 100);

  // Calculate SST amount (6%)
  const sstAmountInCents = Math.round(baseAmountInCents * 0.06);

  // Calculate total amount including SST
  const totalAmountInCents = baseAmountInCents + sstAmountInCents;

  return {
    baseAmountInCents,
    sstAmountInCents,
    totalAmountInCents
  };
};

/**
 * Calculate the complete subscription price including prorated and full month periods
 * @param subscriptionPackage The subscription package
 * @param teamSize Number of team members
 * @param isYearly Whether the billing is yearly
 * @param billingDate Date when billing starts (defaults to current date)
 * @returns A structured response with all pricing details
 */
export const calculateSubscriptionPrice = (
  subscriptionPackage: SubscriptionPackageEntity,
  teamSize: number,
  isYearly: boolean,
  billingDate: Date = new Date()
): SubscriptionPriceResult => {
  if (!subscriptionPackage) {
    throw new Error('Subscription package is required');
  }

  // Ensure valid inputs
  const validTeamSize = Math.max(1, teamSize || 1); // Minimum 1 user
  const validIsYearly = Boolean(isYearly);

  // Base monthly price per member (amount is already price per member)
  const monthlyPrice = subscriptionPackage.amount;

  // Discounted monthly price per member (if yearly billing)
  const discountedMonthlyPrice = validIsYearly
    ? Math.round(monthlyPrice * (1 - YEARLY_DISCOUNT_RATE))
    : monthlyPrice;

  // Total monthly cost for all members
  const monthlyTotal = discountedMonthlyPrice * validTeamSize;

  // Calculate dates
  const currentDate = moment(billingDate);
  const currentAnchorDate = moment(billingDate).date(27);
  if (currentDate.date() > 27) {
    currentAnchorDate.add(1, 'month');
  }
  const nextMonthAnchorDate = moment(currentAnchorDate).add(1, 'month');

  // Determine billing scenarios
  const isFirstDayOfMonth = currentDate.date() === 1;
  const isAfterMidMonth = currentDate.date() > 14;
  const dayOfMonth = currentDate.date();

  // Determine if proration should be applied for the first period
  // Proration applies only for dates 15th through 27th (inclusive)
  // - No proration for dates 1st through 14th (charge until next 27th)
  // - No proration for dates after 27th (handled as next billing cycle)
  const shouldProrate = dayOfMonth > 14 && dayOfMonth <= 27;

  // Calculate first period amount (prorated or initial month)
  const firstPeriodResult = calculatePeriodPrice(
    monthlyTotal,
    shouldProrate,
    validIsYearly,
    currentDate,
    currentAnchorDate
  );

  // Calculate full month amount
  const fullMonthResult = calculatePeriodPrice(
    monthlyTotal,
    false, // not prorated - full amount
    validIsYearly,
    currentAnchorDate,
    nextMonthAnchorDate
  );

  // Calculate combined amounts
  const combinedBaseAmountInCents = firstPeriodResult.baseAmountInCents + fullMonthResult.baseAmountInCents;
  const combinedSstAmountInCents = firstPeriodResult.sstAmountInCents + fullMonthResult.sstAmountInCents;
  const combinedTotalAmountInCents = firstPeriodResult.totalAmountInCents + fullMonthResult.totalAmountInCents;

  // Convert amounts to RM for display
  const firstPeriodBaseAmount = +(firstPeriodResult.baseAmountInCents / 100).toFixed(2);
  const firstPeriodSstAmount = +(firstPeriodResult.sstAmountInCents / 100).toFixed(2);
  const firstPeriodTotalAmount = +(firstPeriodResult.totalAmountInCents / 100).toFixed(2);

  const fullMonthBaseAmount = +(fullMonthResult.baseAmountInCents / 100).toFixed(2);
  const fullMonthSstAmount = +(fullMonthResult.sstAmountInCents / 100).toFixed(2);
  const fullMonthTotalAmount = +(fullMonthResult.totalAmountInCents / 100).toFixed(2);

  const combinedBaseAmount = +(combinedBaseAmountInCents / 100).toFixed(2);
  const combinedSstAmount = +(combinedSstAmountInCents / 100).toFixed(2);
  const combinedTotalAmount = +(combinedTotalAmountInCents / 100).toFixed(2);

  // Determine the label for the first period
  const firstPeriodLabel = shouldProrate ? "Prorated period" : "Initial month";

  // Format dates for display
  const formatDate = (date: moment.Moment): string => date.format('YYYY-MM-DD');
  const formatDisplayDate = (date: moment.Moment): string => date.format('D MMM');

  return {
    firstPeriod: {
      startDate: currentDate.toDate(),
      endDate: currentAnchorDate.toDate(),
      baseAmountInCents: firstPeriodResult.baseAmountInCents,
      sstAmountInCents: firstPeriodResult.sstAmountInCents,
      totalAmountInCents: firstPeriodResult.totalAmountInCents,
      baseAmount: firstPeriodBaseAmount,
      sstAmount: firstPeriodSstAmount,
      totalAmount: firstPeriodTotalAmount,
      isProrated: shouldProrate,
      label: firstPeriodLabel,
      formattedStartDate: formatDisplayDate(currentDate),
      formattedEndDate: formatDisplayDate(currentAnchorDate)
    },
    fullMonthPeriod: {
      startDate: currentAnchorDate.toDate(),
      endDate: nextMonthAnchorDate.toDate(),
      baseAmountInCents: fullMonthResult.baseAmountInCents,
      sstAmountInCents: fullMonthResult.sstAmountInCents,
      totalAmountInCents: fullMonthResult.totalAmountInCents,
      baseAmount: fullMonthBaseAmount,
      sstAmount: fullMonthSstAmount,
      totalAmount: fullMonthTotalAmount,
      formattedStartDate: formatDisplayDate(currentAnchorDate),
      formattedEndDate: formatDisplayDate(nextMonthAnchorDate)
    },
    combined: {
      baseAmountInCents: combinedBaseAmountInCents,
      sstAmountInCents: combinedSstAmountInCents,
      totalAmountInCents: combinedTotalAmountInCents,
      baseAmount: combinedBaseAmount,
      sstAmount: combinedSstAmount,
      totalAmount: combinedTotalAmount
    },
    subscriptionEndDate: nextMonthAnchorDate.toDate(),
    formattedSubscriptionEndDate: formatDate(nextMonthAnchorDate),
    isFirstDayOfMonth,
    isAfterMidMonth,
    dayOfMonth
  };
};