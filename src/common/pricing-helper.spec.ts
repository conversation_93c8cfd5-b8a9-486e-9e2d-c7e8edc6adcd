import { calculateSubscriptionPrice } from './pricing-helper';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';

describe('Pricing Helper - Proration Logic (Improved & Extended)', () => {
  const mockSubscriptionPackage = {
    id: 1,
    amount: 100, // RM 100 per member per month
    title: 'Basic',
    description: 'Basic package',
    availableDuration: 30,
    totalProjects: 10,
    totalUsers: 10,
    storage: 1000,
    isProjectBased: false,
    features: ['feature1', 'feature2'],
    nonFeatures: ['nonfeature1'],
    allowTask: true,
    allowProjectDocument: true,
    allowWorkProgramme: true,
    allowCorrespondence: true,
    allowWorkspaceDocument: true,
    allowWorkspaceTemplate: true,
    allowDrawing: true,
    allowBimModel: false,
    allowPhoto: true,
    allowScheduleChart: false,
    allowScheduleActivity: false,
    allowDashboard: true,
    allowEmailCorrespondence: true,
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    createdBy: 1,
    updatedBy: 1,
    deletedBy: null,
    recordSource: 'Web' as any,
    salesOrders: [],
    companySubscriptions: []
  };

  // Helper function to call calculateSubscriptionPrice with proper typing
  const calculatePrice = (billingDate: Date, isYearly = false, teamSize = 1, packageAmount = 100) => {
    const pkg = packageAmount !== 100 ? { ...mockSubscriptionPackage, amount: packageAmount } : mockSubscriptionPackage;
    return calculateSubscriptionPrice(pkg as SubscriptionPackageEntity, teamSize, isYearly, billingDate);
  };

  // Helper function to run tests with common assertions
  const runTest = ({ day, month = 0, year = 2024, isYearly = false, teamSize = 1, packageAmount = 100, expected }: {
    day: number;
    month?: number;
    year?: number;
    isYearly?: boolean;
    teamSize?: number;
    packageAmount?: number;
    expected: {
      isProrated: boolean;
      label: string;
      baseAmount?: number;
      sstAmount?: number;
      totalAmount?: number;
      formattedStartDate?: string;
      formattedEndDate?: string;
      subscriptionEndDate?: string;
    };
  }) => {
    const billingDate = new Date(year, month, day);
    const result = calculatePrice(billingDate, isYearly, teamSize, packageAmount);

    expect(result.firstPeriod.isProrated).toBe(expected.isProrated);
    expect(result.firstPeriod.label).toBe(expected.label);
    expect(result.dayOfMonth).toBe(day);
    if (expected.baseAmount !== undefined) {
      expect(result.firstPeriod.baseAmount).toBeCloseTo(expected.baseAmount, 2);
    }
    if (expected.sstAmount !== undefined) {
      expect(result.firstPeriod.sstAmount).toBeCloseTo(expected.sstAmount, 2);
    }
    if (expected.totalAmount !== undefined) {
      expect(result.firstPeriod.totalAmount).toBeCloseTo(expected.totalAmount, 2);
    }
    if (expected.formattedStartDate !== undefined) {
      expect(result.firstPeriod.formattedStartDate).toBe(expected.formattedStartDate);
    }
    if (expected.formattedEndDate !== undefined) {
      expect(result.firstPeriod.formattedEndDate).toBe(expected.formattedEndDate);
    }
    if (expected.subscriptionEndDate !== undefined) {
      expect(result.formattedSubscriptionEndDate).toBe(expected.subscriptionEndDate);
    }
  };

  describe('Boundary Dates & Calculation', () => {
    it('should NOT apply proration for 1st day of month', () => {
      runTest({
        day: 1,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should NOT apply proration for 14th day of month', () => {
      runTest({
        day: 14,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should apply proration for 15th day of month', () => {
      // Prorated: (100/27)*12 = 44.44, SST: 2.67, Total: 47.11
      runTest({
        day: 15,
        expected: {
          isProrated: true,
          label: 'Prorated period',
          baseAmount: 44.44,
          sstAmount: 2.67,
          totalAmount: 47.11
        },
      });
    });
    it('should apply proration for 26th day of month (last prorated day)', () => {
      // Prorated: (100/27)*1 = 3.70, SST: 0.22, Total: 3.92
      runTest({
        day: 26,
        expected: {
          isProrated: true,
          label: 'Prorated period',
          baseAmount: 3.70,
          sstAmount: 0.22,
          totalAmount: 3.92
        },
      });
    });
    it('should NOT apply proration for 27th day of month (anchor date)', () => {
      // 27th is the anchor date - should charge full amount until next 27th
      runTest({
        day: 27,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should NOT apply proration for 28th day of month', () => {
      runTest({
        day: 28,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
  });

  describe('Different Months', () => {
    it('should handle April (30 days) - 27th', () => {
      runTest({
        day: 27, month: 3, // April
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should handle June (30 days) - 28th', () => {
      runTest({
        day: 28, month: 5, // June
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should handle February (non-leap year, 28 days)', () => {
      runTest({
        day: 28, month: 1, // February
        year: 2023,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
    it('should handle February (leap year, 29 days)', () => {
      runTest({
        day: 29, month: 1, // February
        year: 2024,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00
        },
      });
    });
  });

  describe('Yearly Billing', () => {
    it('should not apply proration for yearly billing at 1st', () => {
      // Assume yearly discount 10%, so monthly base 90, total for year: 1080
      runTest({
        day: 1, isYearly: true, teamSize: 1,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 90.00, // check your YEARLY_DISCOUNT_RATE
          sstAmount: 5.40,
          totalAmount: 95.40,
        }
      });
    });
    it('should apply proration for yearly billing at 15th', () => {
      runTest({
        day: 15, isYearly: true, teamSize: 1,
        expected: {
          isProrated: true,
          label: 'Prorated period',
        }
      });
    });
  });

  describe('Team Size Variation', () => {
    it('should correctly apply for teamSize = 3, 1st day', () => {
      runTest({
        day: 1, teamSize: 3,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 300.00,
          sstAmount: 18.00,
          totalAmount: 318.00,
        }
      });
    });
    it('should correctly apply for teamSize = 5, 20th day (prorated)', () => {
      // Prorated: (500/27)*7 = 129.63, SST: 7.78, Total: 137.41
      runTest({
        day: 20, teamSize: 5,
        expected: {
          isProrated: true,
          label: 'Prorated period',
          baseAmount: 129.63,
          sstAmount: 7.78,
          totalAmount: 137.41
        }
      });
    });
  });

  describe('Formatting', () => {
    it('should format the start and end dates correctly', () => {
      runTest({
        day: 10, month: 0, year: 2024,
        expected: {
          isProrated: false,
          label: 'Initial month',
          formattedStartDate: '10 Jan',
          formattedEndDate: '27 Jan',
        }
      });
    });
    it('should return correct formattedSubscriptionEndDate', () => {
      runTest({
        day: 10, month: 0, year: 2024,
        expected: {
          isProrated: false,
          label: 'Initial month',
          subscriptionEndDate: '2024-02-27', // billing date: 10 Jan 2024, next anchor: 27 Feb 2024
        }
      });
    });
  });

  describe('Custom Package Amount', () => {
    it('should calculate correctly for package amount 250', () => {
      runTest({
        day: 1, packageAmount: 250,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 250.00,
          sstAmount: 15.00,
          totalAmount: 265.00,
        }
      });
    });
  });

  describe('Negative/Zero Team Size', () => {
    it('should default team size to 1 if zero', () => {
      runTest({
        day: 1, teamSize: 0,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00,
        }
      });
    });
    it('should default team size to 1 if negative', () => {
      runTest({
        day: 1, teamSize: -10,
        expected: {
          isProrated: false,
          label: 'Initial month',
          baseAmount: 100.00,
          sstAmount: 6.00,
          totalAmount: 106.00,
        }
      });
    });
  });
});
