import { defaultQueryOptions } from '@constants';
import { Authorize, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { CompanySubscriptionEntity } from '../entity/company-subscription.entity';
import { SubscriptionPackageDto } from '@modules/subscription-package/dto/subscription-package.gql.dto';
import { relationOption } from '@constants/query.constant';
import { CompanySubscriptionAuthorizer } from '../company-subscription.authorizer';
import moment from 'moment';

@ObjectType('CompanySubscription')
@Authorize(CompanySubscriptionAuthorizer)
@Relation('subscriptionPackage', () => SubscriptionPackageDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class CompanySubscriptionDto extends CompanySubscriptionEntity {
  @Field(() => Boolean, { nullable: true })
  get isSubscriptionActive() {
    try {
      console.log('isSubscriptionActive', this.subscriptionEndDate, moment().subtract(1, "days").toDate());
      if (!this.subscriptionEndDate) return false;
      return this.subscriptionEndDate > moment().subtract(1, "days").toDate()
    } catch (error) {
      console.error('Error in isSubscriptionActive:', error);
      return false;
    }
  }

  // 10 DAYS GRACE PERIOD AFTER END OF SUBSCRIPTION PERIOD
  @Field(() => Boolean, { nullable: true })
  get isSubscriptionInGracePeriod() {
    try {

      console.log('isSubscriptionInGracePeriod', this.subscriptionEndDate, moment().subtract(10, "days").toDate());

      if (!this.subscriptionEndDate) return false;
      return new Date() > this.subscriptionEndDate && this.subscriptionEndDate > moment().subtract(10, "days").toDate();
    } catch (error) {
      console.error('Error in isSubscriptionInGracePeriod:', error);
      return false;
    }
  }

  @Field(() => Boolean, { nullable: true })
  isFreeTrial?: boolean;

  @Field(() => Boolean, { nullable: true })
  get isTrialEnding() {
    try {
      if (!this.isFreeTrial || !this.subscriptionEndDate) return false;
      const daysLeft = moment(this.subscriptionEndDate).diff(moment(), 'days');
      return daysLeft <= 3 && daysLeft >= 0;
    } catch (error) {
      console.error('Error in isTrialEnding:', error);
      return false;
    }
  }

  @Field(() => Number, { nullable: true })
  get daysLeftInTrial() {
    try {
      if (!this.isFreeTrial || !this.subscriptionEndDate) return null;
      return Math.max(0, moment(this.subscriptionEndDate).diff(moment(), 'days'));
    } catch (error) {
      console.error('Error in daysLeftInTrial:', error);
      return null;
    }
  }

  @Field(() => Date, { nullable: true })
  get nextPaymentDate() {
    return this.nextBillingDate || this.subscriptionEndDate;
  }

  // nextBillingAmount is now handled by a custom resolver in company-subscription.resolver.ts
  // This ensures the subscriptionPackage relation is always loaded when needed
}

@InputType()
export class CreateCompanySubscriptionInputDTO {
  @IDField(() => ID) subscriptionPackageId: number;
  @IDField(() => ID) companyId: number;
  subscriptionEndDate: Date;
  seatCount?: number;
  isYearly?: boolean;
  nextBillingDate?: Date;
}

@InputType()
export class UpdateCompanySubscriptionInputDTO extends PartialType(CreateCompanySubscriptionInputDTO) {}

@InputType()
export class ChangeSeatCountInputDTO {
  @IDField(() => ID) subscriptionId: number;
  newSeatCount: number;
}
